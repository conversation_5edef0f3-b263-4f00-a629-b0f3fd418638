import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/HomeView.vue')
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/LoginView.vue')
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../views/DashboardView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/schedules',
    name: 'Schedules',
    component: () => import('../views/ScheduleView.vue'),
    meta: { requiresAuth: true }
  },

  {
    path: '/teacher-dashboard',
    name: 'TeacherDashboard',
    component: () => import('../views/TeacherDashboardView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/schedules/create',
    name: 'CreateSchedule',
    component: () => import('../views/CreateScheduleView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/schedules/edit/:id',
    name: 'EditSchedule',
    component: () => import('../views/EditScheduleViewSimple.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/test-api',
    name: 'TestAPI',
    component: () => import('../views/TestAPIView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/calendar',
    name: 'Calendar',
    component: () => import('../views/CalendarView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/instructors',
    name: 'Instructors',
    component: () => import('../views/InstructorView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/rooms',
    name: 'Rooms',
    component: () => import('../views/RoomView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/subjects',
    name: 'Subjects',
    component: () => import('../views/SubjectView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/classes',
    name: 'Classes',
    component: () => import('../views/ClassView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/campuses',
    name: 'Campuses',
    component: () => import('../views/CampusView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/departments',
    name: 'Departments',
    component: () => import('../views/DepartmentView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/majors',
    name: 'Majors',
    component: () => import('../views/MajorView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/academic-years',
    name: 'AcademicYears',
    component: () => import('../views/AcademicYearView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/semesters',
    name: 'Semesters',
    component: () => import('../views/SemesterView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/time-slots',
    name: 'TimeSlots',
    component: () => import('../views/TimeSlotView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/user-management',
    name: 'UserManagement',
    component: () => import('../views/UserManagementView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/data-import',
    name: 'DataImport',
    component: () => import('../views/DataImportView.vue'),
    meta: { requiresAuth: true }
  },

]

export default routes
