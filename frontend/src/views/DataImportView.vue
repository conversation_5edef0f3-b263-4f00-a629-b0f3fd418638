<template>
  <div class="data-import-view">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-2">
            Khởi tạo dữ liệu ban đầu
          </h1>
          <p class="text-gray-600">
            Import dữ liệu ban đầu từ file Excel để khởi tạo hệ thống
          </p>
        </div>

        <!-- Download Template Section -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h2 class="text-xl font-semibold text-blue-900 mb-4">
            📥 Tải xuống template Excel
          </h2>
          <p class="text-blue-700 mb-4">
            Tải xuống file template Excel mẫu để chuẩn bị dữ liệu import
          </p>
          <button
            @click="downloadTemplate"
            :disabled="downloading"
            class="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-2 rounded-lg font-medium transition-colors"
          >
            <span v-if="downloading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Đang tải...
            </span>
            <span v-else>
              📄 Tải template Excel
            </span>
          </button>
        </div>

        <!-- Upload Section -->
        <div class="bg-white border border-gray-200 rounded-lg p-6 mb-8">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">
            📤 Upload file Excel
          </h2>
          
          <!-- File Upload Area -->
          <div
            @drop="handleDrop"
            @dragover.prevent
            @dragenter.prevent
            class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
            :class="{ 'border-blue-400 bg-blue-50': dragOver }"
          >
            <div v-if="!selectedFile">
              <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              <p class="text-lg text-gray-600 mb-2">
                Kéo thả file Excel vào đây hoặc
              </p>
              <label class="cursor-pointer">
                <span class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                  Chọn file
                </span>
                <input
                  type="file"
                  class="hidden"
                  accept=".xlsx,.xls"
                  @change="handleFileSelect"
                >
              </label>
              <p class="text-sm text-gray-500 mt-2">
                Chỉ hỗ trợ file .xlsx và .xls
              </p>
            </div>
            
            <div v-else class="text-center">
              <div class="flex items-center justify-center mb-4">
                <svg class="h-8 w-8 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-lg font-medium text-gray-900">{{ selectedFile.name }}</span>
              </div>
              <p class="text-sm text-gray-500 mb-4">
                Kích thước: {{ formatFileSize(selectedFile.size) }}
              </p>
              <div class="flex justify-center space-x-4">
                <button
                  @click="importData"
                  :disabled="importing"
                  class="bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                >
                  <span v-if="importing" class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Đang import...
                  </span>
                  <span v-else>
                    🚀 Bắt đầu import
                  </span>
                </button>
                <button
                  @click="clearFile"
                  :disabled="importing"
                  class="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                >
                  Hủy
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Import Results -->
        <div v-if="importResults" class="bg-white border border-gray-200 rounded-lg p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">
            📊 Kết quả import
          </h2>
          
          <!-- Success Summary -->
          <div v-if="importResults.success" class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <div class="flex items-center mb-2">
              <svg class="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span class="font-medium text-green-800">Import thành công!</span>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div v-for="(count, type) in importResults.imported" :key="type" class="text-center">
                <div class="font-bold text-green-700 text-lg">{{ count }}</div>
                <div class="text-green-600 capitalize">{{ formatImportType(type) }}</div>
              </div>
            </div>
          </div>

          <!-- Warnings -->
          <div v-if="importResults.warnings && importResults.warnings.length > 0" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <h3 class="font-medium text-yellow-800 mb-2">⚠️ Cảnh báo:</h3>
            <ul class="text-sm text-yellow-700 space-y-1">
              <li v-for="warning in importResults.warnings" :key="warning">
                • {{ warning }}
              </li>
            </ul>
          </div>

          <!-- Errors -->
          <div v-if="importResults.errors && importResults.errors.length > 0" class="bg-red-50 border border-red-200 rounded-lg p-4">
            <h3 class="font-medium text-red-800 mb-2">❌ Lỗi:</h3>
            <ul class="text-sm text-red-700 space-y-1 max-h-40 overflow-y-auto">
              <li v-for="error in importResults.errors" :key="error">
                • {{ error }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { dataImportAPI } from '@/services/api'

// Reactive data
const downloading = ref(false)
const importing = ref(false)
const selectedFile = ref<File | null>(null)
const importResults = ref<any>(null)
const dragOver = ref(false)

// Methods
const downloadTemplate = async () => {
  try {
    downloading.value = true
    const response = await dataImportAPI.downloadTemplate()

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', 'initial_data_template.xlsx')
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Error downloading template:', error)
    alert('Có lỗi xảy ra khi tải template')
  } finally {
    downloading.value = false
  }
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    selectedFile.value = target.files[0]
    importResults.value = null
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  dragOver.value = false
  
  if (event.dataTransfer?.files && event.dataTransfer.files[0]) {
    const file = event.dataTransfer.files[0]
    if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
      selectedFile.value = file
      importResults.value = null
    } else {
      alert('Chỉ hỗ trợ file Excel (.xlsx, .xls)')
    }
  }
}

const clearFile = () => {
  selectedFile.value = null
  importResults.value = null
}

const importData = async () => {
  if (!selectedFile.value) return

  try {
    importing.value = true
    const response = await dataImportAPI.importData(selectedFile.value)
    importResults.value = response.data
  } catch (error: any) {
    console.error('Error importing data:', error)
    importResults.value = {
      success: false,
      errors: [error.response?.data?.error || 'Có lỗi xảy ra khi import dữ liệu']
    }
  } finally {
    importing.value = false
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatImportType = (type: string): string => {
  const typeMap: Record<string, string> = {
    campuses: 'Cơ sở',
    departments: 'Khoa/Phòng ban',
    majors: 'Ngành học',
    academic_years: 'Năm học',
    semesters: 'Học kỳ',
    time_slots: 'Ca học',
    rooms: 'Phòng học',
    instructors: 'Giảng viên',
    subjects: 'Môn học',
    classes: 'Lớp học'
  }
  return typeMap[type] || type
}
</script>

<style scoped>
.data-import-view {
  min-height: 100vh;
  background-color: #f9fafb;
}
</style>
