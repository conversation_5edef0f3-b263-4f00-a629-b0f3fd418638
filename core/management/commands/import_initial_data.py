from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from core.models import (
    Role, Campus, Department, Major, AcademicYear, Semester,
    Class, Subject, Lesson, Instructor, Room, TimeSlot, PracticeGroup
)
from datetime import datetime, time
import openpyxl
import os

User = get_user_model()


class Command(BaseCommand):
    help = 'Import initial data from Excel file'

    def add_arguments(self, parser):
        parser.add_argument(
            'excel_file',
            type=str,
            help='Path to Excel file containing initial data'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before import',
        )

    def handle(self, *args, **options):
        excel_file = options['excel_file']
        clear_data = options['clear']

        if not os.path.exists(excel_file):
            raise CommandError(f'Excel file "{excel_file}" does not exist.')

        try:
            workbook = openpyxl.load_workbook(excel_file)
            self.stdout.write(f'Successfully loaded Excel file: {excel_file}')
            
            if clear_data:
                self.clear_existing_data()
            
            # Import data in order of dependencies
            self.import_campuses(workbook)
            self.import_departments(workbook)
            self.import_majors(workbook)
            self.import_academic_years(workbook)
            self.import_semesters(workbook)
            self.import_time_slots(workbook)
            self.import_rooms(workbook)
            self.import_instructors(workbook)
            self.import_subjects(workbook)
            self.import_classes(workbook)
            
            self.stdout.write(
                self.style.SUCCESS('Successfully imported all data from Excel file')
            )
            
        except Exception as e:
            raise CommandError(f'Error importing data: {str(e)}')

    def clear_existing_data(self):
        """Clear existing data in reverse dependency order"""
        self.stdout.write('Clearing existing data...')
        
        # Clear in reverse order of dependencies
        Class.objects.all().delete()
        Subject.objects.all().delete()
        Instructor.objects.all().delete()
        Room.objects.all().delete()
        TimeSlot.objects.all().delete()
        Semester.objects.all().delete()
        AcademicYear.objects.all().delete()
        Major.objects.all().delete()
        Department.objects.all().delete()
        Campus.objects.all().delete()
        
        self.stdout.write(self.style.SUCCESS('Cleared existing data'))

    def import_campuses(self, workbook):
        """Import campus data from Excel"""
        if 'Campuses' not in workbook.sheetnames:
            self.stdout.write(self.style.WARNING('No "Campuses" sheet found, skipping...'))
            return

        sheet = workbook['Campuses']
        self.stdout.write('Importing campuses...')
        
        # Skip header row
        for row in sheet.iter_rows(min_row=2, values_only=True):
            if not row[0]:  # Skip empty rows
                continue
                
            code, name, address, phone, email = row[:5]
            
            campus, created = Campus.objects.get_or_create(
                code=code,
                defaults={
                    'name': name,
                    'address': address or '',
                    'phone': phone or '',
                    'email': email or ''
                }
            )
            
            if created:
                self.stdout.write(f'  Created campus: {campus.name}')
            else:
                self.stdout.write(f'  Campus already exists: {campus.name}')

    def import_departments(self, workbook):
        """Import department data from Excel"""
        if 'Departments' not in workbook.sheetnames:
            self.stdout.write(self.style.WARNING('No "Departments" sheet found, skipping...'))
            return

        sheet = workbook['Departments']
        self.stdout.write('Importing departments...')
        
        for row in sheet.iter_rows(min_row=2, values_only=True):
            if not row[0]:
                continue
                
            code, name, campus_code = row[:3]
            
            try:
                campus = Campus.objects.get(code=campus_code)
                department, created = Department.objects.get_or_create(
                    code=code,
                    defaults={
                        'name': name,
                        'campus': campus
                    }
                )
                
                if created:
                    self.stdout.write(f'  Created department: {department.name}')
                else:
                    self.stdout.write(f'  Department already exists: {department.name}')
                    
            except Campus.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'  Campus "{campus_code}" not found for department "{name}"')
                )

    def import_majors(self, workbook):
        """Import major data from Excel"""
        if 'Majors' not in workbook.sheetnames:
            self.stdout.write(self.style.WARNING('No "Majors" sheet found, skipping...'))
            return

        sheet = workbook['Majors']
        self.stdout.write('Importing majors...')
        
        for row in sheet.iter_rows(min_row=2, values_only=True):
            if not row[0]:
                continue
                
            code, name, department_code = row[:3]
            
            try:
                department = Department.objects.get(code=department_code)
                major, created = Major.objects.get_or_create(
                    code=code,
                    defaults={
                        'name': name,
                        'department': department
                    }
                )
                
                if created:
                    self.stdout.write(f'  Created major: {major.name}')
                else:
                    self.stdout.write(f'  Major already exists: {major.name}')
                    
            except Department.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'  Department "{department_code}" not found for major "{name}"')
                )

    def import_academic_years(self, workbook):
        """Import academic year data from Excel"""
        if 'AcademicYears' not in workbook.sheetnames:
            self.stdout.write(self.style.WARNING('No "AcademicYears" sheet found, skipping...'))
            return

        sheet = workbook['AcademicYears']
        self.stdout.write('Importing academic years...')
        
        for row in sheet.iter_rows(min_row=2, values_only=True):
            if not row[0]:
                continue
                
            year_code, start_date, end_date, is_current = row[:4]
            
            # Convert Excel date to Python date
            if isinstance(start_date, datetime):
                start_date = start_date.date()
            if isinstance(end_date, datetime):
                end_date = end_date.date()
            
            academic_year, created = AcademicYear.objects.get_or_create(
                year_code=year_code,
                defaults={
                    'start_date': start_date,
                    'end_date': end_date,
                    'is_current': bool(is_current) if is_current else False
                }
            )
            
            if created:
                self.stdout.write(f'  Created academic year: {academic_year.year_code}')
            else:
                self.stdout.write(f'  Academic year already exists: {academic_year.year_code}')

    def import_semesters(self, workbook):
        """Import semester data from Excel"""
        if 'Semesters' not in workbook.sheetnames:
            self.stdout.write(self.style.WARNING('No "Semesters" sheet found, skipping...'))
            return

        sheet = workbook['Semesters']
        self.stdout.write('Importing semesters...')
        
        for row in sheet.iter_rows(min_row=2, values_only=True):
            if not row[0]:
                continue
                
            semester_code, name, academic_year_code, start_date, end_date, is_current = row[:6]
            
            try:
                academic_year = AcademicYear.objects.get(year_code=academic_year_code)
                
                # Convert Excel date to Python date
                if isinstance(start_date, datetime):
                    start_date = start_date.date()
                if isinstance(end_date, datetime):
                    end_date = end_date.date()
                
                semester, created = Semester.objects.get_or_create(
                    semester_code=semester_code,
                    defaults={
                        'name': name,
                        'academic_year': academic_year,
                        'start_date': start_date,
                        'end_date': end_date,
                        'is_current': bool(is_current) if is_current else False
                    }
                )
                
                if created:
                    self.stdout.write(f'  Created semester: {semester.name}')
                else:
                    self.stdout.write(f'  Semester already exists: {semester.name}')
                    
            except AcademicYear.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'  Academic year "{academic_year_code}" not found for semester "{name}"')
                )

    def import_time_slots(self, workbook):
        """Import time slot data from Excel"""
        if 'TimeSlots' not in workbook.sheetnames:
            self.stdout.write(self.style.WARNING('No "TimeSlots" sheet found, skipping...'))
            return

        sheet = workbook['TimeSlots']
        self.stdout.write('Importing time slots...')

        for row in sheet.iter_rows(min_row=2, values_only=True):
            if not row[0]:
                continue

            slot_name, session, start_time, end_time, duration_hours = row[:5]

            # Convert Excel time to Python time
            if isinstance(start_time, datetime):
                start_time = start_time.time()
            if isinstance(end_time, datetime):
                end_time = end_time.time()

            time_slot, created = TimeSlot.objects.get_or_create(
                slot_name=slot_name,
                defaults={
                    'session': session,
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration_hours': float(duration_hours) if duration_hours else 1.5
                }
            )

            if created:
                self.stdout.write(f'  Created time slot: {time_slot.slot_name}')
            else:
                self.stdout.write(f'  Time slot already exists: {time_slot.slot_name}')

    def import_rooms(self, workbook):
        """Import room data from Excel"""
        if 'Rooms' not in workbook.sheetnames:
            self.stdout.write(self.style.WARNING('No "Rooms" sheet found, skipping...'))
            return

        sheet = workbook['Rooms']
        self.stdout.write('Importing rooms...')

        for row in sheet.iter_rows(min_row=2, values_only=True):
            if not row[0]:
                continue

            code, name, capacity, room_type, campus_code = row[:5]

            try:
                campus = Campus.objects.get(code=campus_code)

                room, created = Room.objects.get_or_create(
                    code=code,
                    defaults={
                        'name': name,
                        'capacity': int(capacity) if capacity else 30,
                        'room_type': room_type or 'CLASSROOM',
                        'campus': campus
                    }
                )

                if created:
                    self.stdout.write(f'  Created room: {room.name}')
                else:
                    self.stdout.write(f'  Room already exists: {room.name}')

            except Campus.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'  Campus "{campus_code}" not found for room "{name}"')
                )

    def import_instructors(self, workbook):
        """Import instructor data from Excel"""
        if 'Instructors' not in workbook.sheetnames:
            self.stdout.write(self.style.WARNING('No "Instructors" sheet found, skipping...'))
            return

        sheet = workbook['Instructors']
        self.stdout.write('Importing instructors...')

        for row in sheet.iter_rows(min_row=2, values_only=True):
            if not row[0]:
                continue

            employee_code, full_name, email, phone, department_code = row[:5]

            try:
                department = Department.objects.get(code=department_code)

                instructor, created = Instructor.objects.get_or_create(
                    employee_code=employee_code,
                    defaults={
                        'full_name': full_name,
                        'email': email or '',
                        'phone': phone or '',
                        'department': department
                    }
                )

                if created:
                    self.stdout.write(f'  Created instructor: {instructor.full_name}')
                else:
                    self.stdout.write(f'  Instructor already exists: {instructor.full_name}')

            except Department.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'  Department "{department_code}" not found for instructor "{full_name}"')
                )

    def import_subjects(self, workbook):
        """Import subject data from Excel"""
        if 'Subjects' not in workbook.sheetnames:
            self.stdout.write(self.style.WARNING('No "Subjects" sheet found, skipping...'))
            return

        sheet = workbook['Subjects']
        self.stdout.write('Importing subjects...')

        for row in sheet.iter_rows(min_row=2, values_only=True):
            if not row[0]:
                continue

            code, name, credits, theory_hours, practice_hours, department_code = row[:6]

            try:
                department = Department.objects.get(code=department_code)

                subject, created = Subject.objects.get_or_create(
                    code=code,
                    defaults={
                        'name': name,
                        'credits': int(credits) if credits else 3,
                        'theory_hours': int(theory_hours) if theory_hours else 30,
                        'practice_hours': int(practice_hours) if practice_hours else 0,
                        'department': department
                    }
                )

                if created:
                    self.stdout.write(f'  Created subject: {subject.name}')
                else:
                    self.stdout.write(f'  Subject already exists: {subject.name}')

            except Department.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'  Department "{department_code}" not found for subject "{name}"')
                )

    def import_classes(self, workbook):
        """Import class data from Excel"""
        if 'Classes' not in workbook.sheetnames:
            self.stdout.write(self.style.WARNING('No "Classes" sheet found, skipping...'))
            return

        sheet = workbook['Classes']
        self.stdout.write('Importing classes...')

        for row in sheet.iter_rows(min_row=2, values_only=True):
            if not row[0]:
                continue

            code, name, student_count, major_code, academic_year_code = row[:5]

            try:
                major = Major.objects.get(code=major_code)
                academic_year = AcademicYear.objects.get(year_code=academic_year_code)

                class_obj, created = Class.objects.get_or_create(
                    code=code,
                    defaults={
                        'name': name,
                        'student_count': int(student_count) if student_count else 30,
                        'major': major,
                        'academic_year': academic_year
                    }
                )

                if created:
                    self.stdout.write(f'  Created class: {class_obj.name}')
                else:
                    self.stdout.write(f'  Class already exists: {class_obj.name}')

            except Major.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'  Major "{major_code}" not found for class "{name}"')
                )
            except AcademicYear.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'  Academic year "{academic_year_code}" not found for class "{name}"')
                )
