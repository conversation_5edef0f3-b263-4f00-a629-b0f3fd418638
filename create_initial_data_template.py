#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create Excel template for initial data import
"""

import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from datetime import date, time, datetime

def create_template():
    """Create Excel template with sample data"""
    
    # Create workbook
    wb = openpyxl.Workbook()
    
    # Remove default sheet
    wb.remove(wb.active)
    
    # Header style
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Create Campuses sheet
    ws_campuses = wb.create_sheet("Campuses")
    campuses_headers = ["Code", "Name", "Address", "Phone", "Email"]
    campuses_data = [
        ["HN", "Cơ sở Hà Nội", "Số 8 Tôn <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "024-3123-4567", "<EMAIL>"],
        ["HCM", "<PERSON>ơ sở TP.HCM", "<PERSON>ố 123 <PERSON><PERSON><PERSON><PERSON>, Quận 5, TP.HCM", "028-3123-4567", "<EMAIL>"],
        ["DN", "Cơ sở Đà Nẵng", "Số 456 Nguyễn Hữu Thọ, Hải Châu, Đà Nẵng", "0236-3123-456", "<EMAIL>"]
    ]
    
    # Add headers
    for col, header in enumerate(campuses_headers, 1):
        cell = ws_campuses.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # Add data
    for row, data in enumerate(campuses_data, 2):
        for col, value in enumerate(data, 1):
            ws_campuses.cell(row=row, column=col, value=value)
    
    # Auto-adjust column widths
    for column in ws_campuses.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws_campuses.column_dimensions[column_letter].width = adjusted_width

    # Create Departments sheet
    ws_departments = wb.create_sheet("Departments")
    departments_headers = ["Code", "Name", "Campus_Code"]
    departments_data = [
        ["CNTT", "Khoa Công nghệ thông tin", "HN"],
        ["KT", "Khoa Kế toán", "HN"],
        ["QT", "Khoa Quản trị kinh doanh", "HCM"],
        ["NN", "Khoa Ngoại ngữ", "DN"]
    ]
    
    # Add headers
    for col, header in enumerate(departments_headers, 1):
        cell = ws_departments.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # Add data
    for row, data in enumerate(departments_data, 2):
        for col, value in enumerate(data, 1):
            ws_departments.cell(row=row, column=col, value=value)
    
    # Auto-adjust column widths
    for column in ws_departments.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws_departments.column_dimensions[column_letter].width = adjusted_width

    # Create Majors sheet
    ws_majors = wb.create_sheet("Majors")
    majors_headers = ["Code", "Name", "Department_Code"]
    majors_data = [
        ["CNTT", "Công nghệ thông tin", "CNTT"],
        ["KTPM", "Kỹ thuật phần mềm", "CNTT"],
        ["KT", "Kế toán", "KT"],
        ["QTKD", "Quản trị kinh doanh", "QT"],
        ["TA", "Tiếng Anh", "NN"]
    ]
    
    # Add headers and data for Majors
    for col, header in enumerate(majors_headers, 1):
        cell = ws_majors.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    for row, data in enumerate(majors_data, 2):
        for col, value in enumerate(data, 1):
            ws_majors.cell(row=row, column=col, value=value)
    
    # Auto-adjust column widths
    for column in ws_majors.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws_majors.column_dimensions[column_letter].width = adjusted_width

    # Create AcademicYears sheet
    ws_academic_years = wb.create_sheet("AcademicYears")
    academic_years_headers = ["Year_Code", "Start_Date", "End_Date", "Is_Current"]
    academic_years_data = [
        ["2023-2024", date(2023, 9, 1), date(2024, 6, 30), False],
        ["2024-2025", date(2024, 9, 1), date(2025, 6, 30), True],
        ["2025-2026", date(2025, 9, 1), date(2026, 6, 30), False]
    ]
    
    # Add headers and data for Academic Years
    for col, header in enumerate(academic_years_headers, 1):
        cell = ws_academic_years.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    for row, data in enumerate(academic_years_data, 2):
        for col, value in enumerate(data, 1):
            ws_academic_years.cell(row=row, column=col, value=value)
    
    # Auto-adjust column widths
    for column in ws_academic_years.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws_academic_years.column_dimensions[column_letter].width = adjusted_width

    # Create Semesters sheet
    ws_semesters = wb.create_sheet("Semesters")
    semesters_headers = ["Semester_Code", "Name", "Academic_Year_Code", "Start_Date", "End_Date", "Is_Current"]
    semesters_data = [
        ["HK1_2024-2025", "Học kỳ 1", "2024-2025", date(2024, 9, 1), date(2025, 1, 15), True],
        ["HK2_2024-2025", "Học kỳ 2", "2024-2025", date(2025, 1, 16), date(2025, 6, 30), False],
        ["HK3_2024-2025", "Học kỳ hè", "2024-2025", date(2025, 7, 1), date(2025, 8, 31), False]
    ]
    
    # Add headers and data for Semesters
    for col, header in enumerate(semesters_headers, 1):
        cell = ws_semesters.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    for row, data in enumerate(semesters_data, 2):
        for col, value in enumerate(data, 1):
            ws_semesters.cell(row=row, column=col, value=value)
    
    # Auto-adjust column widths
    for column in ws_semesters.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws_semesters.column_dimensions[column_letter].width = adjusted_width

    # Create TimeSlots sheet
    ws_time_slots = wb.create_sheet("TimeSlots")
    time_slots_headers = ["Slot_Name", "Session", "Start_Time", "End_Time", "Duration_Hours"]
    time_slots_data = [
        ["Ca 1", "SANG", time(7, 0), time(8, 30), 1.5],
        ["Ca 2", "SANG", time(8, 45), time(10, 15), 1.5],
        ["Ca 3", "SANG", time(10, 30), time(12, 0), 1.5],
        ["Ca 4", "CHIEU", time(13, 0), time(14, 30), 1.5],
        ["Ca 5", "CHIEU", time(14, 45), time(16, 15), 1.5],
        ["Ca 6", "CHIEU", time(16, 30), time(18, 0), 1.5],
        ["Ca 7", "TOI", time(18, 30), time(20, 0), 1.5],
        ["Ca 8", "TOI", time(20, 15), time(21, 45), 1.5]
    ]
    
    # Add headers and data for Time Slots
    for col, header in enumerate(time_slots_headers, 1):
        cell = ws_time_slots.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    for row, data in enumerate(time_slots_data, 2):
        for col, value in enumerate(data, 1):
            ws_time_slots.cell(row=row, column=col, value=value)
    
    # Auto-adjust column widths
    for column in ws_time_slots.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws_time_slots.column_dimensions[column_letter].width = adjusted_width

    # Create Rooms sheet
    ws_rooms = wb.create_sheet("Rooms")
    rooms_headers = ["Code", "Name", "Capacity", "Room_Type", "Campus_Code"]
    rooms_data = [
        ["A101", "Phòng học A101", 40, "CLASSROOM", "HN"],
        ["A102", "Phòng học A102", 35, "CLASSROOM", "HN"],
        ["LAB01", "Phòng thí nghiệm 1", 25, "LAB", "HN"],
        ["B201", "Phòng học B201", 50, "CLASSROOM", "HCM"],
        ["C301", "Phòng học C301", 30, "CLASSROOM", "DN"]
    ]

    # Add headers and data for Rooms
    for col, header in enumerate(rooms_headers, 1):
        cell = ws_rooms.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment

    for row, data in enumerate(rooms_data, 2):
        for col, value in enumerate(data, 1):
            ws_rooms.cell(row=row, column=col, value=value)

    # Auto-adjust column widths
    for column in ws_rooms.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws_rooms.column_dimensions[column_letter].width = adjusted_width

    # Create Instructors sheet
    ws_instructors = wb.create_sheet("Instructors")
    instructors_headers = ["Employee_Code", "Full_Name", "Email", "Phone", "Department_Code"]
    instructors_data = [
        ["GV001", "Nguyễn Văn An", "<EMAIL>", "0123456789", "CNTT"],
        ["GV002", "Trần Thị Bình", "<EMAIL>", "0123456790", "CNTT"],
        ["GV003", "Lê Văn Cường", "<EMAIL>", "0123456791", "KT"],
        ["GV004", "Phạm Thị Dung", "<EMAIL>", "0123456792", "QT"],
        ["GV005", "Hoàng Văn Em", "<EMAIL>", "0123456793", "NN"]
    ]

    # Add headers and data for Instructors
    for col, header in enumerate(instructors_headers, 1):
        cell = ws_instructors.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment

    for row, data in enumerate(instructors_data, 2):
        for col, value in enumerate(data, 1):
            ws_instructors.cell(row=row, column=col, value=value)

    # Auto-adjust column widths
    for column in ws_instructors.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws_instructors.column_dimensions[column_letter].width = adjusted_width

    # Create Subjects sheet
    ws_subjects = wb.create_sheet("Subjects")
    subjects_headers = ["Code", "Name", "Credits", "Theory_Hours", "Practice_Hours", "Department_Code"]
    subjects_data = [
        ["CNTT101", "Nhập môn lập trình", 3, 30, 15, "CNTT"],
        ["CNTT102", "Cấu trúc dữ liệu", 3, 30, 15, "CNTT"],
        ["KT101", "Nguyên lý kế toán", 3, 45, 0, "KT"],
        ["QT101", "Quản trị học đại cương", 3, 45, 0, "QT"],
        ["NN101", "Tiếng Anh cơ bản", 3, 30, 15, "NN"]
    ]

    # Add headers and data for Subjects
    for col, header in enumerate(subjects_headers, 1):
        cell = ws_subjects.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment

    for row, data in enumerate(subjects_data, 2):
        for col, value in enumerate(data, 1):
            ws_subjects.cell(row=row, column=col, value=value)

    # Auto-adjust column widths
    for column in ws_subjects.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws_subjects.column_dimensions[column_letter].width = adjusted_width

    # Create Classes sheet
    ws_classes = wb.create_sheet("Classes")
    classes_headers = ["Code", "Name", "Student_Count", "Major_Code", "Academic_Year_Code"]
    classes_data = [
        ["CNTT2024A", "Lớp CNTT 2024A", 35, "CNTT", "2024-2025"],
        ["CNTT2024B", "Lớp CNTT 2024B", 32, "CNTT", "2024-2025"],
        ["KTPM2024A", "Lớp KTPM 2024A", 30, "KTPM", "2024-2025"],
        ["KT2024A", "Lớp KT 2024A", 40, "KT", "2024-2025"],
        ["QTKD2024A", "Lớp QTKD 2024A", 38, "QTKD", "2024-2025"]
    ]

    # Add headers and data for Classes
    for col, header in enumerate(classes_headers, 1):
        cell = ws_classes.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment

    for row, data in enumerate(classes_data, 2):
        for col, value in enumerate(data, 1):
            ws_classes.cell(row=row, column=col, value=value)

    # Auto-adjust column widths
    for column in ws_classes.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws_classes.column_dimensions[column_letter].width = adjusted_width

    # Save the workbook
    wb.save("initial_data_template.xlsx")
    print("Template created successfully: initial_data_template.xlsx")

if __name__ == "__main__":
    create_template()
