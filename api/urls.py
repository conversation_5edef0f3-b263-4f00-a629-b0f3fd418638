from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from . import views
from . import auth_views

router = DefaultRouter()
router.register(r'permissions', views.PermissionViewSet)
router.register(r'roles', views.RoleViewSet)
router.register(r'users', views.UserViewSet)
router.register(r'campuses', views.CampusViewSet)
router.register(r'departments', views.DepartmentViewSet)
router.register(r'majors', views.MajorViewSet)
router.register(r'academic-years', views.AcademicYearViewSet)
router.register(r'semesters', views.SemesterViewSet)
router.register(r'classes', views.ClassViewSet)
router.register(r'subjects', views.SubjectViewSet)
router.register(r'lessons', views.LessonViewSet)
router.register(r'instructors', views.InstructorViewSet)
router.register(r'rooms', views.RoomViewSet)
router.register(r'time-slots', views.TimeSlotViewSet)
router.register(r'practice-groups', views.PracticeGroupViewSet)
router.register(r'schedules', views.ScheduleViewSet)
router.register(r'schedule-conflicts', views.ScheduleConflictViewSet)
router.register(r'data-import', views.DataImportViewSet, basename='data-import')

urlpatterns = [
    # Auth endpoints
    path('auth/login/', auth_views.login, name='auth_login'),
    path('auth/logout/', auth_views.logout, name='auth_logout'),
    path('auth/user/', auth_views.user_profile, name='auth_user'),
    path('auth/profile/', auth_views.update_profile, name='auth_update_profile'),
    path('auth/change-password/', auth_views.change_password, name='auth_change_password'),

    # API endpoints
    path('', include(router.urls)),
]
